import { createClient } from '@supabase/supabase-js';

const supabaseUrl =
  process.env.NEXT_PUBLIC_SUPABASE_URL ||
  "https://djyzepqwkquomocdgnpd.supabase.co";
const supabaseAnonKey =
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ||
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.uvxsYk9vFB5eiRpnPBCrj63YmQfK9SZD_CpRvvlmxcs";

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    storageKey: "supabase-auth",
    autoRefreshToken: true,
    detectSessionInUrl: true,
  },
});

// Type definitions matching your Supabase schema
export interface Provider {
  id: string;
  name: string;
  created_at: string;
}

export interface Faculty {
  id: string;
  provider_id: string;
  name: string;
  created_at: string;
}

export interface Course {
  id: string;
  faculty_id: string;
  course_name: string;
  vet_code: string | null;
  cricos_code: string;
  created_at: string;
  // Populated by joins
  faculty?: Faculty;
  provider?: Provider;
  intakes?: Intake[];
  locations?: Location[];
}

export interface Location {
  id: string;
  name: string;
  code: string | null;
  created_at: string;
}

export interface Intake {
  id: string;
  course_id: string;
  location_id: string;
  intake_date: string;
  created_at: string;
  // Populated by joins
  course?: Course;
  location?: Location;
}

// Legacy compatibility types for existing components
export interface LegacyCourse {
  courseName: string;
  intakes: { date: string }[];
}

export interface LegacyCollege {
  collegeName: string;
  courses: LegacyCourse[];
}
