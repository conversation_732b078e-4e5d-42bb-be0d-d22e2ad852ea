import { supabase } from "./client";

// Use the shared supabase client from client.ts to avoid multiple instances
export const supabaseAuth = supabase;

// Types for authentication
export interface AuthUser {
  id: string;
  email: string;
  role?: string;
  user_metadata?: {
    full_name?: string;
  };
}

// Auth helper functions
export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabaseAuth.auth.signInWithPassword({
    email,
    password,
  });

  return { data, error };
};

export const signOut = async () => {
  const { error } = await supabaseAuth.auth.signOut();
  return { error };
};

export const getCurrentUser = async () => {
  const {
    data: { session },
    error,
  } = await supabaseAuth.auth.getSession();

  if (error || !session) {
    return { user: null, error };
  }

  return { user: session.user, error: null };
};

export const resetPassword = async (email: string) => {
  const { data, error } = await supabaseAuth.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/reset-password`,
  });

  return { data, error };
};

export const updatePassword = async (password: string) => {
  const { data, error } = await supabaseAuth.auth.updateUser({
    password,
  });

  return { data, error };
};
