import { differenceInDays, parseISO, format, addDays } from "date-fns";

export interface FeeAllocationInput {
  // Student details
  studentId: string;
  studentName: string;
  courseName: string;
  courseCode: string;

  // Financial details
  totalTuitionFee: number;
  tuitionFeesPaidSoFar: number;
  materialsFeesPaid: number;

  // Date details
  originalCourseStartDate: string;
  defermentStartDate: string;
  courseCommencementDate?: string; // For scenarios where course hasn't started
}

export interface FeeAllocationResult {
  // Calculated amounts
  unspentTuitionFees: number;
  isStudentInCredit: boolean;
  amountOwed: number; // Negative if in credit, positive if owes money

  // New CoE calculations
  newTotalTuitionFee: number;
  initialDeposit: number;

  // Pro-rata calculations
  courseCompletionPercentage: number;
  feesEarnedByInstitution: number;

  // Status and messages
  canProceedWithDeferment: boolean;
  statusMessage: string;

  // Email template data
  emailData: {
    studentName: string;
    studentId: string;
    courseName: string;
    courseCode: string;
    defermentFromDate: string;
    totalTuitionFee: number;
    tuitionFeesPaidUntilNow: number;
    unspentTuitionFees: number;
    materialsFeesPaid: number;
  };
}

/**
 * Calculate fee allocation using portal's exact priority system
 * Priority Order: Placement → Material → Tuition (REVERSE of typical expectation)
 */
export function calculatePortalFeeAllocation(
  prepaymentTotal: number,
  totalTuitionFee: number,
  totalMaterialFee: number,
  totalPlacementFee: number
): {
  prepaidTuitionFee: number;
  prepaidMaterialFee: number;
  prepaidPlacementFee: number;
  otherPrePaidNonTuitionFee: number;
} {
  let remainingAmount = prepaymentTotal;

  // Step 1: Allocate to Placement Fee first
  const prepaidPlacementFee = Math.min(remainingAmount, totalPlacementFee);
  remainingAmount -= prepaidPlacementFee;

  // Step 2: Allocate to Material Fee second
  const prepaidMaterialFee = Math.min(remainingAmount, totalMaterialFee);
  remainingAmount -= prepaidMaterialFee;

  // Step 3: Allocate remaining to Tuition Fee
  const prepaidTuitionFee = Math.min(remainingAmount, totalTuitionFee);

  // Calculate other pre-paid non-tuition fee (Material + Placement)
  const otherPrePaidNonTuitionFee = prepaidMaterialFee + prepaidPlacementFee;

  return {
    prepaidTuitionFee,
    prepaidMaterialFee,
    prepaidPlacementFee,
    otherPrePaidNonTuitionFee,
  };
}

/**
 * Calculate fee coverage period using portal's exact method
 * This matches the portal calculator's approach: fee proportion → day coverage
 */
export function calculateFeeCoveragePeriod(
  courseStartDate: string,
  courseDurationWeeks: number,
  totalTuitionFee: number,
  prepaidTuitionFee: number
): {
  coverageStartDate: string;
  coverageEndDate: string;
  coverageWeeks: number;
  coverageDays: number;
} {
  if (totalTuitionFee <= 0 || prepaidTuitionFee <= 0) {
    return {
      coverageStartDate: "",
      coverageEndDate: "",
      coverageWeeks: 0,
      coverageDays: 0,
    };
  }

  const startDate = parseISO(courseStartDate);

  // Calculate total course days (portal uses weeks * 7 - 1)
  // For 52 weeks, portal shows 363 days, not 364
  const totalCourseDays = courseDurationWeeks * 7 - 1;

  // Calculate coverage percentage
  const coveragePercentage = prepaidTuitionFee / totalTuitionFee;

  // If coverage is 100% or more, cover the entire course
  if (coveragePercentage >= 1) {
    const endDate = addDays(startDate, totalCourseDays);

    return {
      coverageStartDate: format(startDate, "dd/MM/yyyy"),
      coverageEndDate: format(endDate, "dd/MM/yyyy"),
      coverageWeeks: courseDurationWeeks,
      coverageDays: totalCourseDays + 1,
    };
  }

  // Calculate coverage days using portal's method
  const coverageDays = Math.floor(coveragePercentage * totalCourseDays);
  const endDate = addDays(startDate, coverageDays);
  const coverageWeeks = Math.round(coverageDays / 7);

  return {
    coverageStartDate: format(startDate, "dd/MM/yyyy"),
    coverageEndDate: format(endDate, "dd/MM/yyyy"),
    coverageWeeks,
    coverageDays: coverageDays + 1, // +1 to make it inclusive
  };
}

/**
 * Calculate pro-rata fees based on course completion percentage
 */
export function calculateProRataFees(
  originalStartDate: string,
  defermentStartDate: string,
  totalCourseDuration: number, // in days
  totalTuitionFee: number
): {
  completionPercentage: number;
  feesEarned: number;
  remainingFees: number;
} {
  const startDate = parseISO(originalStartDate);
  const deferDate = parseISO(defermentStartDate);

  const daysCompleted = differenceInDays(deferDate, startDate);
  const completionPercentage = Math.max(
    0,
    Math.min(100, (daysCompleted / totalCourseDuration) * 100)
  );

  const feesEarned = (completionPercentage / 100) * totalTuitionFee;
  const remainingFees = totalTuitionFee - feesEarned;

  return {
    completionPercentage,
    feesEarned,
    remainingFees,
  };
}

/**
 * Main fee allocation calculation function
 */
export function calculateFeeAllocation(
  input: FeeAllocationInput
): FeeAllocationResult {
  const {
    studentId,
    studentName,
    courseName,
    courseCode,
    totalTuitionFee,
    tuitionFeesPaidSoFar,
    materialsFeesPaid,
    originalCourseStartDate,
    defermentStartDate,
    courseCommencementDate,
  } = input;

  // Scenario 1: Course hasn't started yet (like Benjamin's case)
  if (
    courseCommencementDate &&
    parseISO(defermentStartDate) < parseISO(courseCommencementDate)
  ) {
    const unspentTuitionFees = tuitionFeesPaidSoFar;
    const isStudentInCredit = unspentTuitionFees > 0;

    return {
      unspentTuitionFees,
      isStudentInCredit,
      amountOwed: -unspentTuitionFees, // Negative means credit
      newTotalTuitionFee: totalTuitionFee,
      initialDeposit: unspentTuitionFees,
      courseCompletionPercentage: 0,
      feesEarnedByInstitution: 0,
      canProceedWithDeferment: true,
      statusMessage: `Student has not started the course yet. $${unspentTuitionFees.toFixed(
        2
      )} will be allocated as initial deposit for the new CoE.`,
      emailData: {
        studentName,
        studentId,
        courseName,
        courseCode,
        defermentFromDate: format(parseISO(defermentStartDate), "dd/MM/yyyy"),
        totalTuitionFee,
        tuitionFeesPaidUntilNow: tuitionFeesPaidSoFar,
        unspentTuitionFees,
        materialsFeesPaid,
      },
    };
  }

  // Scenario 2 & 3: Course has started - calculate pro-rata
  // Assuming standard course duration (this could be made configurable)
  const standardCourseDurationDays = 365; // 1 year course

  const proRataCalc = calculateProRataFees(
    originalCourseStartDate,
    defermentStartDate,
    standardCourseDurationDays,
    totalTuitionFee
  );

  const feesEarnedByInstitution = proRataCalc.feesEarned;
  const unspentTuitionFees = tuitionFeesPaidSoFar - feesEarnedByInstitution;
  const isStudentInCredit = unspentTuitionFees > 0;
  const amountOwed = -unspentTuitionFees; // Negative if credit, positive if owes

  // Calculate new CoE amounts using the formula: A - B + C
  // A = Original total tuition fee
  // B = Tuition fees paid so far
  // C = Unspent tuition fees (if positive)
  const newTotalTuitionFee = isStudentInCredit
    ? totalTuitionFee - tuitionFeesPaidSoFar + unspentTuitionFees
    : totalTuitionFee;

  const initialDeposit = Math.max(0, unspentTuitionFees);

  let statusMessage = "";
  let canProceedWithDeferment = true;

  if (isStudentInCredit) {
    statusMessage = `Student has completed ${proRataCalc.completionPercentage.toFixed(
      1
    )}% of the course. $${unspentTuitionFees.toFixed(
      2
    )} will be allocated as initial deposit for the new CoE.`;
  } else {
    canProceedWithDeferment = false;
    statusMessage = `Student has completed ${proRataCalc.completionPercentage.toFixed(
      1
    )}% of the course but owes $${Math.abs(unspentTuitionFees).toFixed(
      2
    )}. Payment must be made before deferment can proceed.`;
  }

  return {
    unspentTuitionFees,
    isStudentInCredit,
    amountOwed,
    newTotalTuitionFee,
    initialDeposit,
    courseCompletionPercentage: proRataCalc.completionPercentage,
    feesEarnedByInstitution,
    canProceedWithDeferment,
    statusMessage,
    emailData: {
      studentName,
      studentId,
      courseName,
      courseCode,
      defermentFromDate: format(parseISO(defermentStartDate), "dd/MM/yyyy"),
      totalTuitionFee,
      tuitionFeesPaidUntilNow: tuitionFeesPaidSoFar,
      unspentTuitionFees,
      materialsFeesPaid,
    },
  };
}

/**
 * Generate email template for AR team
 */
export function generateAREmailTemplate(result: FeeAllocationResult): string {
  const { emailData } = result;

  return `
Subject: Fee Check - ${emailData.studentId} - ${emailData.studentName}

Dear AR Team,

Please find the fee details below for student deferment processing:

Student Details:
- Student Name: ${emailData.studentName}
- Student ID: ${emailData.studentId}
- Course: ${emailData.courseName} (${emailData.courseCode})
- Deferment From Date: ${emailData.defermentFromDate}

Fee Breakdown:
- Total Tuition Fee (as per FLOO): $${emailData.totalTuitionFee.toFixed(2)}
- Tuition Fees Paid Until Now: $${emailData.tuitionFeesPaidUntilNow.toFixed(2)}
- Unspent Tuition Fees: $${emailData.unspentTuitionFees.toFixed(2)}
- Materials Fees Paid: $${emailData.materialsFeesPaid.toFixed(2)}

Status: ${
    result.canProceedWithDeferment
      ? "APPROVED FOR DEFERMENT"
      : "PAYMENT REQUIRED"
  }
${result.statusMessage}

${
  result.canProceedWithDeferment
    ? `New CoE Details:
- New Total Tuition Fee: $${result.newTotalTuitionFee.toFixed(2)}
- Initial Deposit: $${result.initialDeposit.toFixed(2)}`
    : `Action Required: Student must pay $${Math.abs(result.amountOwed).toFixed(
        2
      )} before deferment can be processed.`
}

Kind Regards,
SCV Team
  `.trim();
}

/**
 * Validate fee allocation input
 */
export function validateFeeAllocationInput(
  input: Partial<FeeAllocationInput>
): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Student information is now optional
  if (!input.courseName?.trim()) errors.push("Course name is required");

  if (typeof input.totalTuitionFee !== "number" || input.totalTuitionFee < 0) {
    errors.push("Total tuition fee must be a positive number");
  }

  if (
    typeof input.tuitionFeesPaidSoFar !== "number" ||
    input.tuitionFeesPaidSoFar < 0
  ) {
    errors.push("Tuition fees paid so far must be a positive number");
  }

  if (
    typeof input.materialsFeesPaid !== "number" ||
    input.materialsFeesPaid < 0
  ) {
    errors.push("Materials fees paid must be a positive number");
  }

  if (!input.originalCourseStartDate)
    errors.push("Original course start date is required");
  if (!input.defermentStartDate)
    errors.push("Deferment start date is required");

  // Date validation
  if (input.originalCourseStartDate && input.defermentStartDate) {
    try {
      const startDate = parseISO(input.originalCourseStartDate);
      const deferDate = parseISO(input.defermentStartDate);

      if (deferDate < startDate) {
        errors.push("Deferment start date cannot be before course start date");
      }
    } catch (e) {
      errors.push("Invalid date format");
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
