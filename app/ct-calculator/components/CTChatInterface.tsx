'use client';

import { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageSquare, Send, Bot, User, Copy, Download, Save, Trash2, Info } from 'lucide-react';
import { CTCalculationResult } from '@/lib/ai/ct-calculator';
import MarkdownRenderer from '@/components/ui/MarkdownRenderer';

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  calculations?: CTCalculationResult;
}

interface CTChatInterfaceProps {
  onCalculationUpdate?: (calculation: CTCalculationResult | null) => void;
}

export interface CTChatInterfaceRef {
  sendMessage: (message: string) => void;
}

const CTChatInterface = forwardRef<CTChatInterfaceRef, CTChatInterfaceProps>(
  ({ onCalculationUpdate }, ref) => {

  const getDefaultMessage = (): ChatMessage => ({
    id: '1',
    role: 'assistant',
    content: "Hi! I'm CT Advisor, your Credit Transfer application assistant. I'll help you process CT applications using the correct workflows.\n\n📋 **What I can help with:**\n• First Aid CT applications (invoice-based)\n• General CT applications (unit-based)\n• Change of Qualification (COQ) applications\n\n💡 **To get started:**\nShare the student's details and unit information. You can copy-paste unit tables directly from emails or documents.",
    timestamp: new Date()
  });

  // Load messages from localStorage on component mount
  const loadMessagesFromStorage = (): ChatMessage[] => {
    if (typeof window === 'undefined') return [getDefaultMessage()];

    try {
      const stored = localStorage.getItem('ct-chat-messages');
      if (stored) {
        const parsed = JSON.parse(stored);
        // Convert timestamp strings back to Date objects
        return parsed.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));
      }
    } catch (error) {
      console.error('Error loading chat messages from storage:', error);
    }
    return [getDefaultMessage()];
  };

  const [messages, setMessages] = useState<ChatMessage[]>([getDefaultMessage()]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentCalculation, setCurrentCalculation] = useState<CTCalculationResult | null>(null);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Save messages to localStorage whenever messages change
  const saveMessagesToStorage = (messagesToSave: ChatMessage[]) => {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem('ct-chat-messages', JSON.stringify(messagesToSave));
    } catch (error) {
      console.error('Error saving chat messages to storage:', error);
    }
  };

  // Clear chat history
  const clearChatHistory = () => {
    const defaultMessages = [getDefaultMessage()];
    setIsInitialLoad(false); // Allow scrolling after clearing
    setMessages(defaultMessages);
    saveMessagesToStorage(defaultMessages);
    setCurrentCalculation(null);
    onCalculationUpdate?.(null);
  };

  // Auto-resize textarea
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [input]);

  // Expose sendMessage method to parent component
  useImperativeHandle(ref, () => ({
    sendMessage: (message: string) => {
      setInput(message);
      setTimeout(() => sendMessage(message), 0);
    }
  }));

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Load messages from localStorage on component mount
  useEffect(() => {
    const storedMessages = loadMessagesFromStorage();
    if (storedMessages.length > 1 || storedMessages[0]?.content !== getDefaultMessage().content) {
      setMessages(storedMessages);
    }
    // Don't set isInitialLoad to false here - let the scroll effect handle it
  }, []);

  // Handle scrolling - prevent auto-scroll on initial load
  useEffect(() => {
    if (isInitialLoad) {
      setIsInitialLoad(false);
      return; // Don't scroll on initial load
    }
    scrollToBottom();
  }, [messages, isInitialLoad]);

  const sendMessage = async (messageText?: string) => {
    const messageToSend = messageText || input.trim();
    if (!messageToSend || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: messageToSend,
      timestamp: new Date()
    };

    const newMessages = [...messages, userMessage];
    setIsInitialLoad(false); // Ensure scrolling works for new messages
    setMessages(newMessages);
    saveMessagesToStorage(newMessages);

    if (!messageText) {
      setInput('');
      // Reset textarea height
      setTimeout(() => adjustTextareaHeight(), 0);
    }
    setIsLoading(true);

    try {
      const response = await fetch('/api/chat/ct-advisor', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [...messages, userMessage].map(msg => ({
            role: msg.role,
            content: msg.content
          }))
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: data.content,
        timestamp: new Date(),
        calculations: data.calculations
      };

      const updatedMessages = [...newMessages, assistantMessage];
      setMessages(updatedMessages);
      saveMessagesToStorage(updatedMessages);

      // Update current calculation
      if (data.calculations) {
        setCurrentCalculation(data.calculations);
        onCalculationUpdate?.(data.calculations);
      }
    } catch (error) {
      console.error('Error:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: `❌ **Error:** Failed to process your request. Please try again.\n\nError details: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date()
      };
      const errorMessages = [...newMessages, errorMessage];
      setMessages(errorMessages);
      saveMessagesToStorage(errorMessages);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // Could add a toast notification here
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const exportChatSummary = () => {
    const summary = messages
      .filter(msg => msg.role === 'assistant' && msg.calculations)
      .map(msg => {
        const calc = msg.calculations!;
        return `
CT Calculator Summary
Generated: ${new Date().toLocaleString()}

Workflow: ${calc.workflowType}
Student: ${calc.studentName || 'Not specified'}
Course: ${calc.courseName || 'Not specified'}

Calculation:
Original Fee: $${calc.originalFee.toLocaleString()}
New Fee: $${calc.newFee.toLocaleString()}
Savings: $${calc.savings.toLocaleString()}
Formula: ${calc.formula}

Units Reduction: ${calc.unitsReduction} units
`;
      })
      .join('\n---\n');

    const blob = new Blob([summary], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ct-calculation-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="flex flex-col h-[700px]">
      {/* Chat Header */}
      <CardHeader className="pb-3 px-4 py-3 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="p-1.5 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg">
              <MessageSquare className="w-4 h-4 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg">CT Advisor</CardTitle>
              <p className="text-xs text-gray-600">AI Assistant</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={clearChatHistory}
              className="text-xs px-2 py-1 text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="w-3 h-3" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={exportChatSummary}
              disabled={!currentCalculation}
              className="text-xs px-2 py-1"
            >
              <Download className="w-3 h-3" />
            </Button>
          </div>
        </div>
        {/* Local Storage Notice */}
        <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-lg flex items-center gap-2">
          <Info className="w-3 h-3 text-blue-600 flex-shrink-0" />
          <p className="text-xs text-blue-800">
            Chat history is stored locally in your browser and not sent to our servers.
          </p>
        </div>
      </CardHeader>

      {/* Chat Messages */}
      <CardContent className="flex-1 overflow-y-auto space-y-3 px-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[90%] ${
                message.role === 'user'
                  ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white'
                  : 'bg-gray-50 text-gray-900 border border-gray-200'
              } rounded-xl p-3 shadow-sm`}
            >
              <div className="flex items-start gap-2">
                <div className={`p-1 rounded-md ${
                  message.role === 'user'
                    ? 'bg-white/20'
                    : 'bg-blue-100'
                }`}>
                  {message.role === 'user' ? (
                    <User className="w-3 h-3" />
                  ) : (
                    <Bot className="w-3 h-3 text-blue-600" />
                  )}
                </div>
                <div className="flex-1">
                  {message.role === 'assistant' ? (
                    <MarkdownRenderer
                      content={message.content}
                      className="text-xs leading-relaxed"
                    />
                  ) : (
                    <div className="whitespace-pre-wrap text-xs leading-relaxed">
                      {message.content}
                    </div>
                  )}
                  <div className="flex items-center justify-between mt-2">
                    <div className={`text-xs ${
                      message.role === 'user' ? 'text-white/70' : 'text-gray-500'
                    }`}>
                      {message.timestamp.toLocaleTimeString()}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(message.content)}
                      className={`p-0.5 h-auto ${
                        message.role === 'user'
                          ? 'text-white/70 hover:text-white hover:bg-white/10'
                          : 'text-gray-500 hover:text-gray-700'
                      }`}
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-50 border border-gray-200 rounded-xl p-3 shadow-sm">
              <div className="flex items-center gap-2">
                <div className="p-1 rounded-md bg-blue-100">
                  <Bot className="w-3 h-3 text-blue-600" />
                </div>
                <div className="flex items-center gap-2">
                  <div className="animate-pulse text-xs text-gray-600">
                    Analyzing...
                  </div>
                  <div className="flex gap-1">
                    <div className="w-1.5 h-1.5 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                    <div className="w-1.5 h-1.5 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                    <div className="w-1.5 h-1.5 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </CardContent>

      {/* Chat Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex gap-2">
          <Textarea
            ref={textareaRef}
            value={input}
            onChange={(e) => {
              setInput(e.target.value);
              adjustTextareaHeight();
            }}
            onKeyDown={handleKeyDown}
            placeholder="Type message or paste unit table..."
            className="flex-1 min-h-[50px] max-h-[120px] resize-none text-sm overflow-hidden"
            rows={2}
          />
          <Button
            onClick={() => sendMessage()}
            disabled={isLoading || !input.trim()}
            className="px-4 self-end"
            size="default"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
        <div className="mt-1 flex justify-between items-center text-xs text-gray-500">
          <span>Enter to send • Shift+Enter for new line</span>
          <span className={`${input.length > 1000 ? 'text-orange-500' : input.length > 1500 ? 'text-red-500' : 'text-gray-500'}`}>
            {input.length} characters
          </span>
        </div>
      </div>
    </div>
  );
});

CTChatInterface.displayName = 'CTChatInterface';

export default CTChatInterface;
