"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Calculator, 
  Calendar, 
  DollarSign, 
  Copy, 
  CheckCircle2, 
  TrendingUp,
  Info,
  <PERSON>rk<PERSON>
} from "lucide-react";

interface ResultItem {
  label: string;
  value: string | number;
  type?: "currency" | "date" | "percentage" | "text";
  highlight?: boolean;
  description?: string;
}

interface EnhancedResultsCardProps {
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  results: ResultItem[];
  onCopy?: (text: string) => void;
  variant?: "default" | "success" | "premium";
  className?: string;
}

export function EnhancedResultsCard({
  title,
  subtitle,
  icon,
  results,
  onCopy,
  variant = "default",
  className = "",
}: EnhancedResultsCardProps) {
  const formatValue = (value: string | number, type?: string) => {
    if (type === "currency") {
      const numValue = typeof value === "string" ? parseFloat(value) : value;
      return new Intl.NumberFormat("en-AU", {
        style: "currency",
        currency: "AUD",
      }).format(numValue);
    }
    if (type === "percentage") {
      return `${value}%`;
    }
    return value.toString();
  };

  const getVariantStyles = () => {
    switch (variant) {
      case "success":
        return {
          header: "bg-gradient-to-r from-emerald-50 via-green-50 to-teal-50 border-b border-emerald-200",
          iconBg: "bg-gradient-to-r from-emerald-500 to-green-600",
          accent: "text-emerald-700",
        };
      case "premium":
        return {
          header: "bg-gradient-to-r from-purple-50 via-indigo-50 to-blue-50 border-b border-purple-200",
          iconBg: "bg-gradient-to-r from-purple-500 to-indigo-600",
          accent: "text-purple-700",
        };
      default:
        return {
          header: "bg-gradient-to-r from-slate-50 to-gray-50 border-b border-slate-200",
          iconBg: "bg-gradient-to-r from-slate-500 to-slate-600",
          accent: "text-slate-700",
        };
    }
  };

  const styles = getVariantStyles();

  const generateCopyText = () => {
    const lines = [`${title}${subtitle ? ` - ${subtitle}` : ""}`];
    results.forEach((result) => {
      lines.push(`${result.label}: ${formatValue(result.value, result.type)}`);
    });
    return lines.join("\n");
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={className}
    >
      <Card className="shadow-xl bg-white border-0 rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300">
        <CardHeader className={styles.header}>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-3 ${styles.iconBg} rounded-xl shadow-lg`}>
                {icon || <Calculator className="w-6 h-6 text-white" />}
              </div>
              <div>
                <span className="text-xl font-bold text-slate-800">{title}</span>
                {subtitle && (
                  <p className={`text-sm font-medium mt-1 ${styles.accent}`}>
                    {subtitle}
                  </p>
                )}
              </div>
            </div>
            
            {onCopy && (
              <Button
                onClick={() => onCopy(generateCopyText())}
                variant="ghost"
                size="sm"
                className="text-slate-600 hover:bg-slate-100"
              >
                <Copy className="w-4 h-4" />
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="p-6">
          <div className="space-y-4">
            {results.map((result, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`flex items-center justify-between p-4 rounded-lg border transition-all duration-200 hover:shadow-md ${
                  result.highlight
                    ? "bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200"
                    : "bg-slate-50 border-slate-200 hover:bg-slate-100"
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${
                    result.highlight 
                      ? "bg-blue-100 text-blue-600" 
                      : "bg-slate-200 text-slate-600"
                  }`}>
                    {result.type === "currency" && <DollarSign className="w-4 h-4" />}
                    {result.type === "date" && <Calendar className="w-4 h-4" />}
                    {result.type === "percentage" && <TrendingUp className="w-4 h-4" />}
                    {!result.type && <Info className="w-4 h-4" />}
                  </div>
                  
                  <div>
                    <span className={`font-medium ${
                      result.highlight ? "text-blue-900" : "text-slate-700"
                    }`}>
                      {result.label}
                    </span>
                    {result.description && (
                      <p className="text-xs text-slate-500 mt-1">
                        {result.description}
                      </p>
                    )}
                  </div>
                </div>
                
                <div className="text-right">
                  <span className={`font-bold text-lg ${
                    result.highlight 
                      ? "text-blue-900" 
                      : result.type === "currency" 
                        ? "text-green-700" 
                        : "text-slate-800"
                  }`}>
                    {formatValue(result.value, result.type)}
                  </span>
                  
                  {result.highlight && (
                    <div className="flex items-center gap-1 mt-1">
                      <Sparkles className="w-3 h-3 text-blue-500" />
                      <span className="text-xs text-blue-600 font-medium">
                        Key Result
                      </span>
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
          
          {/* Success indicator */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.5 }}
            className="mt-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg"
          >
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-500 rounded-full">
                <CheckCircle2 className="w-4 h-4 text-white" />
              </div>
              <div>
                <span className="text-sm font-semibold text-green-800">
                  Calculation Complete
                </span>
                <p className="text-xs text-green-700 mt-1">
                  All values calculated using portal-compatible algorithms
                </p>
              </div>
            </div>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
