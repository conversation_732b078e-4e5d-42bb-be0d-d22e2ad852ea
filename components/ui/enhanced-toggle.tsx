"use client";

import React from "react";
import { Calculator, Zap } from "lucide-react";

interface EnhancedToggleProps {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export function EnhancedToggle({
  checked,
  onCheckedChange,
  disabled = false,
  className = "",
}: EnhancedToggleProps) {
  return (
    <div className={`p-3 bg-slate-50 rounded-xl border border-slate-200 ${className}`}>
      {/* Button-style toggle with rounded borders */}
      <div className="flex items-center justify-center">
        <div className="flex bg-white rounded-full p-1 shadow-sm border border-slate-200">
          <button
            type="button"
            onClick={() => onCheckedChange(false)}
            disabled={disabled}
            className={`flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
              !checked
                ? "bg-slate-900 text-white shadow-sm"
                : "text-slate-600 hover:text-slate-900 hover:bg-slate-50"
            }`}
          >
            <Calculator className="w-4 h-4" />
            Basic
          </button>

          <button
            type="button"
            onClick={() => onCheckedChange(true)}
            disabled={disabled}
            className={`flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
              checked
                ? "bg-blue-600 text-white shadow-sm"
                : "text-slate-600 hover:text-slate-900 hover:bg-slate-50"
            }`}
          >
            <Zap className="w-4 h-4" />
            Advanced
          </button>
        </div>
      </div>

      {/* Status indicator */}
      <div className="mt-2 text-center">
        <span className={`text-xs font-medium px-3 py-1 rounded-full ${
          checked
            ? "bg-blue-100 text-blue-800"
            : "bg-slate-100 text-slate-600"
        }`}>
          {checked ? "🚀 Advanced Mode" : "📊 Basic Mode"}
        </span>
      </div>
    </div>
  );
}
