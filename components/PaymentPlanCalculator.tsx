"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Calculator,
  DollarSign,
  Calendar,
  Save,
  RotateCcw,
  Copy,
  AlertCircle,
  CheckCircle2,
} from "lucide-react";
import { format, addDays } from "date-fns";
import { calculateFeeCoveragePeriod, calculatePortalFeeAllocation } from "@/lib/utils/feeAllocationUtils";
import { DatePicker } from "@/components/ui/DatePicker";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface PaymentPlanData {
  courseName: string;
  numberOfWeeks: number;
  courseStartDate: string;
  courseEndDate: string;
  totalMaterialFee: number;
  totalPlacementFee: number;
  totalTuitionFee: number;
  prepaymentTotal: number;
}

interface CalculatedResults {
  numberOfDays: number;
  numberOfMonths: number;
  totalAllFees: number;
  prepaidAmount: number;
  prepaidTuitionFee: number;
  prepaidMaterialFee: number;
  prepaidPlacementFee: number;
  otherPrePaidNonTuitionFee: number;
  coverageStartDate: string;
  coverageEndDate: string;
}

interface Errors {
  [key: string]: string | undefined;
}

const STORAGE_KEY = "payment-plan-calculator-data";

export default function PaymentPlanCalculator() {
  const [formData, setFormData] = useState<PaymentPlanData>({
    courseName: "",
    numberOfWeeks: 0,
    courseStartDate: "",
    courseEndDate: "",
    totalMaterialFee: 0,
    totalPlacementFee: 0,
    totalTuitionFee: 0,
    prepaymentTotal: 0,
  });

  const [calculatedResults, setCalculatedResults] = useState<CalculatedResults>({
    numberOfDays: 0,
    numberOfMonths: 0,
    totalAllFees: 0,
    prepaidAmount: 0,
    prepaidTuitionFee: 0,
    prepaidMaterialFee: 0,
    prepaidPlacementFee: 0,
    otherPrePaidNonTuitionFee: 0,
    coverageStartDate: "",
    coverageEndDate: "",
  });

  const [errors, setErrors] = useState<Errors>({});
  const [isCalculating, setIsCalculating] = useState(false);
  const [lastSaved, setLastSaved] = useState<string>("");

  // Load data from localStorage on component mount
  useEffect(() => {
    try {
      const savedData = localStorage.getItem(STORAGE_KEY);
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        setFormData(parsedData.formData || {
          courseName: "",
          numberOfWeeks: 0,
          tuitionFee: 0,
          applicationFee: 0,
          materialsFee: 0,
          technologyFee: 0,
          otherFees: 0,
          paymentPlan: "full",
          customPayments: [],
        });
        setLastSaved(parsedData.timestamp || "");
      }
    } catch (error) {
      console.error("Error loading saved data:", error);
    }
  }, []);

  // Auto-save to localStorage whenever formData changes
  useEffect(() => {
    if (formData.courseName || formData.numberOfWeeks > 0) {
      try {
        const dataToSave = {
          formData,
          timestamp: new Date().toLocaleString(),
        };
        localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
        setLastSaved(dataToSave.timestamp);
      } catch (error) {
        console.error("Error saving data:", error);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData]);

  // Auto-calculate course end date when start date or weeks change
  useEffect(() => {
    if (formData.courseStartDate && formData.numberOfWeeks > 0) {
      const startDate = new Date(formData.courseStartDate);
      const endDate = addDays(startDate, formData.numberOfWeeks * 7 - 1); // Subtract 1 to get end date
      const formattedEndDate = format(endDate, "yyyy-MM-dd");

      if (formattedEndDate !== formData.courseEndDate) {
        setFormData(prev => ({
          ...prev,
          courseEndDate: formattedEndDate
        }));
      }
    }
  }, [formData.courseStartDate, formData.numberOfWeeks, formData.courseEndDate]);

  const calculateResults = useCallback(() => {
    const {
      courseStartDate,
      courseEndDate,
      numberOfWeeks,
      totalMaterialFee,
      totalPlacementFee,
      totalTuitionFee,
      prepaymentTotal,
    } = formData;

    if (!courseStartDate || !courseEndDate) return;

    const startDate = new Date(courseStartDate);
    const endDate = new Date(courseEndDate);

    // Calculate number of days and months (always calculate these)
    const numberOfDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    const numberOfMonths = Math.round(numberOfDays / 30.44); // Average days per month

    // Basic calculations
    const totalAllFees = totalMaterialFee + totalPlacementFee + totalTuitionFee;
    const prepaidAmount = prepaymentTotal;

    // Use portal's fee allocation logic: Placement → Material → Tuition
    const feeAllocation = calculatePortalFeeAllocation(
      prepaidAmount,
      totalTuitionFee,
      totalMaterialFee,
      totalPlacementFee
    );

    const prepaidTuitionFee = feeAllocation.prepaidTuitionFee;
    const prepaidMaterialFee = feeAllocation.prepaidMaterialFee;
    const prepaidPlacementFee = feeAllocation.prepaidPlacementFee;
    const otherPrePaidNonTuitionFee = feeAllocation.otherPrePaidNonTuitionFee;

    // Coverage period calculation using portal's method
    let coverageStartDate = "";
    let coverageEndDate = "";

    if (totalTuitionFee > 0 && prepaidTuitionFee > 0) {
      // Calculate course duration in weeks from the number of weeks input
      const courseDurationWeeks = numberOfWeeks || Math.round(numberOfDays / 7);

      const coverage = calculateFeeCoveragePeriod(
        courseStartDate,
        courseDurationWeeks,
        totalTuitionFee,
        prepaidTuitionFee
      );

      coverageStartDate = coverage.coverageStartDate;
      coverageEndDate = coverage.coverageEndDate;
    }

    setCalculatedResults({
      numberOfDays,
      numberOfMonths,
      totalAllFees,
      prepaidAmount,
      prepaidTuitionFee,
      prepaidMaterialFee,
      prepaidPlacementFee,
      otherPrePaidNonTuitionFee,
      coverageStartDate,
      coverageEndDate,
    });
  }, [formData]);

  // Real-time calculations whenever relevant data changes
  useEffect(() => {
    if (formData.courseStartDate && formData.courseEndDate) {
      calculateResults();
    }
  }, [
    formData.courseStartDate,
    formData.courseEndDate,
    formData.numberOfWeeks,
    formData.totalMaterialFee,
    formData.totalPlacementFee,
    formData.totalTuitionFee,
    formData.prepaymentTotal,
    calculateResults,
  ]);

  const handleInputChange = (field: keyof PaymentPlanData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validate = (): boolean => {
    const newErrors: Errors = {};
    let isValid = true;

    if (!formData.courseName) {
      newErrors.courseName = "Course name is required.";
      isValid = false;
    }
    if (!formData.numberOfWeeks || formData.numberOfWeeks <= 0) {
      newErrors.numberOfWeeks = "Number of weeks must be greater than 0.";
      isValid = false;
    }
    if (!formData.courseStartDate) {
      newErrors.courseStartDate = "Course start date is required.";
      isValid = false;
    }
    if (formData.totalMaterialFee < 0) {
      newErrors.totalMaterialFee = "Material fee cannot be negative.";
      isValid = false;
    }
    if (formData.totalPlacementFee < 0) {
      newErrors.totalPlacementFee = "Placement fee cannot be negative.";
      isValid = false;
    }
    if (!formData.totalTuitionFee || formData.totalTuitionFee <= 0) {
      newErrors.totalTuitionFee = "Tuition fee must be greater than 0.";
      isValid = false;
    }
    if (formData.prepaymentTotal < 0) {
      newErrors.prepaymentTotal = "Prepayment total cannot be negative.";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleCalculate = () => {
    if (!validate()) {
      return;
    }

    setIsCalculating(true);
    setTimeout(() => {
      calculateResults();
      setIsCalculating(false);
    }, 300);
  };

  const clearAllData = () => {
    if (confirm("Are you sure you want to clear all data? This cannot be undone.")) {
      localStorage.removeItem(STORAGE_KEY);
      setFormData({
        courseName: "",
        numberOfWeeks: 0,
        courseStartDate: "",
        courseEndDate: "",
        totalMaterialFee: 0,
        totalPlacementFee: 0,
        totalTuitionFee: 0,
        prepaymentTotal: 0,
      });
      setCalculatedResults({
        numberOfDays: 0,
        numberOfMonths: 0,
        totalAllFees: 0,
        prepaidAmount: 0,
        prepaidTuitionFee: 0,
        prepaidMaterialFee: 0,
        prepaidPlacementFee: 0,
        otherPrePaidNonTuitionFee: 0,
        coverageStartDate: "",
        coverageEndDate: "",
      });
      setErrors({});
      setLastSaved("");
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (error) {
      console.error("Failed to copy:", error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <div className="min-h-screen py-8 px-4 bg-gradient-to-br from-slate-50 to-blue-50 w-full">
      <div className="max-w-5xl mx-auto">
        <div className="text-center mb-10">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-green-600 to-green-700 rounded-xl shadow-lg">
              <DollarSign className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-green-600 to-green-800 bg-clip-text text-transparent">
              Payment Plan Fee Calculator
            </h1>
          </div>
          <p className="max-w-2xl mx-auto text-lg text-slate-600 leading-relaxed">
            Calculate payment plan coverage periods and fee allocations for student courses
          </p>
        </div>

        <div className="space-y-6">
          {/* Course Information Section */}
          <Card className="shadow-xl bg-white border-0 rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-md">
                  <Calculator className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold text-slate-800">Course Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="col-span-full">
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    Course Name
                  </label>
                  <Input
                    value={formData.courseName}
                    onChange={(e) => handleInputChange("courseName", e.target.value)}
                    placeholder="Enter course name..."
                    className="w-full"
                  />
                  {errors.courseName && (
                    <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      {errors.courseName}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    Number of Weeks
                  </label>
                  <Input
                    type="number"
                    value={formData.numberOfWeeks || ""}
                    onChange={(e) => handleInputChange("numberOfWeeks", parseInt(e.target.value) || 0)}
                    placeholder="Enter number of weeks..."
                    className="w-full"
                  />
                  {errors.numberOfWeeks && (
                    <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      {errors.numberOfWeeks}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    Course Start Date
                  </label>
                  <DatePicker
                    value={formData.courseStartDate}
                    onChange={(date) => handleInputChange("courseStartDate", date)}
                    className="w-full"
                  />
                  {errors.courseStartDate && (
                    <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      {errors.courseStartDate}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    Course End Date
                  </label>
                  <Input
                    value={formData.courseEndDate ? format(new Date(formData.courseEndDate), "dd/MM/yyyy") : ""}
                    readOnly
                    className="w-full bg-gray-50"
                    placeholder="Auto-calculated"
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    Number of Days
                  </label>
                  <Input
                    value={calculatedResults.numberOfDays || ""}
                    readOnly
                    className="w-full bg-red-50 font-semibold text-red-700"
                    placeholder="Auto-calculated"
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    Number of Months
                  </label>
                  <Input
                    value={calculatedResults.numberOfMonths || ""}
                    readOnly
                    className="w-full bg-red-50 font-semibold text-red-700"
                    placeholder="Auto-calculated"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Fee Input Section */}
          <Card className="shadow-xl bg-white border-0 rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300">
            <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-100">
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-md">
                  <DollarSign className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold text-slate-800">Fee Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    Total Material Fee
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    value={formData.totalMaterialFee || ""}
                    onChange={(e) => handleInputChange("totalMaterialFee", parseFloat(e.target.value) || 0)}
                    placeholder="0.00"
                    className="w-full bg-blue-50 border-blue-200"
                  />
                  {errors.totalMaterialFee && (
                    <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      {errors.totalMaterialFee}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    Total Placement Fee
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    value={formData.totalPlacementFee || ""}
                    onChange={(e) => handleInputChange("totalPlacementFee", parseFloat(e.target.value) || 0)}
                    placeholder="0.00"
                    className="w-full bg-blue-50 border-blue-200"
                  />
                  {errors.totalPlacementFee && (
                    <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      {errors.totalPlacementFee}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    Total Tuition Fee
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    value={formData.totalTuitionFee || ""}
                    onChange={(e) => handleInputChange("totalTuitionFee", parseFloat(e.target.value) || 0)}
                    placeholder="0.00"
                    className="w-full bg-blue-50 border-blue-200"
                  />
                  {errors.totalTuitionFee && (
                    <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      {errors.totalTuitionFee}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    Prepayment Total
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    value={formData.prepaymentTotal || ""}
                    onChange={(e) => handleInputChange("prepaymentTotal", parseFloat(e.target.value) || 0)}
                    placeholder="0.00"
                    className="w-full bg-blue-50 border-blue-200"
                  />
                  {errors.prepaymentTotal && (
                    <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      {errors.prepaymentTotal}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Calculated Results Section */}
          {(calculatedResults.totalAllFees > 0 || formData.totalTuitionFee > 0) && (
            <Card className="shadow-xl bg-white border-0 rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300">
              <CardHeader className="bg-gradient-to-r from-red-50 to-rose-50 border-b border-red-100">
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-red-500 to-red-600 rounded-lg shadow-md">
                    <CheckCircle2 className="w-5 h-5 text-white" />
                  </div>
                  <span className="text-xl font-bold text-slate-800">Calculated Results</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-slate-700 mb-3">
                      Total All Fees
                    </label>
                    <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                      <span className="font-bold text-red-700">
                        {formatCurrency(calculatedResults.totalAllFees)}
                      </span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(calculatedResults.totalAllFees.toString())}
                        className="h-6 w-6 p-0 hover:bg-red-100"
                      >
                        <Copy className="w-3 h-3 text-red-600" />
                      </Button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-slate-700 mb-3">
                      Prepaid Amount
                    </label>
                    <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                      <span className="font-bold text-red-700">
                        {formatCurrency(calculatedResults.prepaidAmount)}
                      </span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(calculatedResults.prepaidAmount.toString())}
                        className="h-6 w-6 p-0 hover:bg-red-100"
                      >
                        <Copy className="w-3 h-3 text-red-600" />
                      </Button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-slate-700 mb-3">
                      Prepaid Tuition Fee
                    </label>
                    <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                      <span className="font-bold text-red-700">
                        {formatCurrency(calculatedResults.prepaidTuitionFee)}
                      </span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(calculatedResults.prepaidTuitionFee.toString())}
                        className="h-6 w-6 p-0 hover:bg-red-100"
                      >
                        <Copy className="w-3 h-3 text-red-600" />
                      </Button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-slate-700 mb-3">
                      Prepaid Material Fee
                    </label>
                    <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                      <span className="font-bold text-red-700">
                        {formatCurrency(calculatedResults.prepaidMaterialFee)}
                      </span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(calculatedResults.prepaidMaterialFee.toString())}
                        className="h-6 w-6 p-0 hover:bg-red-100"
                      >
                        <Copy className="w-3 h-3 text-red-600" />
                      </Button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-slate-700 mb-3">
                      Prepaid Placement Fee
                    </label>
                    <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                      <span className="font-bold text-red-700">
                        {formatCurrency(calculatedResults.prepaidPlacementFee)}
                      </span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(calculatedResults.prepaidPlacementFee.toString())}
                        className="h-6 w-6 p-0 hover:bg-red-100"
                      >
                        <Copy className="w-3 h-3 text-red-600" />
                      </Button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-slate-700 mb-3">
                      Other Pre-Paid Non Tuition Fee
                    </label>
                    <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                      <span className="font-bold text-red-700">
                        {formatCurrency(calculatedResults.otherPrePaidNonTuitionFee)}
                      </span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(calculatedResults.otherPrePaidNonTuitionFee.toString())}
                        className="h-6 w-6 p-0 hover:bg-red-100"
                      >
                        <Copy className="w-3 h-3 text-red-600" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Coverage Period Section */}
          {calculatedResults.coverageStartDate && (
            <Card className="shadow-xl bg-white border-0 rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300">
              <CardHeader className="bg-gradient-to-r from-purple-50 to-indigo-50 border-b border-purple-100">
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-md">
                    <Calendar className="w-5 h-5 text-white" />
                  </div>
                  <span className="text-xl font-bold text-slate-800">Coverage Period</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 p-6">
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    Period Covered by Prepaid Tuition Fee
                  </label>
                  <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border-2 border-purple-200 p-4 rounded-xl">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div>
                          <span className="text-sm font-medium text-purple-700">Start Date:</span>
                          <div className="text-lg font-bold text-purple-800">
                            {calculatedResults.coverageStartDate}
                          </div>
                        </div>
                        <div className="text-purple-500">→</div>
                        <div>
                          <span className="text-sm font-medium text-purple-700">End Date:</span>
                          <div className="text-lg font-bold text-purple-800">
                            {calculatedResults.coverageEndDate}
                          </div>
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(`${calculatedResults.coverageStartDate} - ${calculatedResults.coverageEndDate}`)}
                        className="h-8 w-8 p-0 hover:bg-purple-100"
                      >
                        <Copy className="w-4 h-4 text-purple-600" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Save Status */}
        {lastSaved && (
          <div className="mt-6 text-center">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 border border-green-200 rounded-lg">
              <Save className="w-4 h-4 text-green-600" />
              <span className="text-sm text-green-700 font-medium">
                Auto-saved: {lastSaved}
              </span>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="mt-10 flex flex-col sm:flex-row justify-center gap-4">
          <Button
            onClick={handleCalculate}
            disabled={isCalculating}
            size="lg"
            className="px-12 py-4 text-xl font-bold bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            {isCalculating ? (
              <>
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3" />
                Calculating...
              </>
            ) : (
              <>
                <Calculator className="mr-3 h-6 w-6" />
                Calculate
              </>
            )}
          </Button>

          <Button
            onClick={clearAllData}
            variant="outline"
            size="lg"
            className="px-8 py-4 text-lg font-semibold border-2 border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
          >
            <RotateCcw className="mr-3 h-5 w-5" />
            Clear All Data
          </Button>
        </div>
      </div>
    </div>
  );
}
